//
//  HabitChartView.swift
//  TimeScale
//
//  Created by Augment Agent on 2025/8/26.
//

import SwiftUI
import Charts

// MARK: - 图表数据模型

struct HabitChartData: Identifiable {
    let id = UUID()
    let date: Date
    let value: Double
    let category: String
    let color: Color
    
    init(date: Date, value: Double, category: String = "完成", color: Color = .blue) {
        self.date = date
        self.value = value
        self.category = category
        self.color = color
    }
}

// MARK: - 习惯图表视图

struct HabitChartView: View {
    let habit: Habit
    let dateRange: [Date]
    let comparisonRange: [Date]?
    let chartType: HabitChartType
    
    @Environment(\.dismiss) private var dismiss
    
    init(habit: Habit, dateRange: [Date], comparisonRange: [Date]? = nil, chartType: HabitChartType = .line) {
        self.habit = habit
        self.dateRange = dateRange
        self.comparisonRange = comparisonRange
        self.chartType = chartType
    }
    
    var body: some View {
        NavigationView {
            ScrollView {
                VStack(spacing: 24) {
                    // 图表标题
                    chartHeader
                    
                    // 主图表
                    mainChart
                    
                    // 统计摘要
                    statisticsSummary
                    
                    // 其他图表类型
                    additionalCharts
                }
                .padding()
            }
            .navigationTitle("\(habit.name) 数据分析")
            .navigationBarTitleDisplayMode(.large)
            .toolbar {
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("完成") {
                        dismiss()
                    }
                }
            }
        }
    }
    
    // MARK: - 图表标题
    
    private var chartHeader: some View {
        VStack(alignment: .leading, spacing: 8) {
            HStack {
                Circle()
                    .fill(Color(hex: habit.colorHex))
                    .frame(width: 12, height: 12)
                
                Text(habit.name)
                    .font(.title2)
                    .fontWeight(.semibold)
                
                Spacer()
            }
            
            Text(dateRangeDescription)
                .font(.caption)
                .foregroundColor(.secondary)
        }
    }
    
    // MARK: - 主图表
    
    private var mainChart: some View {
        VStack(alignment: .leading, spacing: 16) {
            Text("完成趋势")
                .font(.headline)
            
            Chart {
                // 当前周期数据
                ForEach(currentPeriodData) { data in
                    switch chartType {
                    case .line:
                        LineMark(
                            x: .value("日期", data.date),
                            y: .value("完成", data.value)
                        )
                        .foregroundStyle(Color(hex: habit.colorHex))
                        .symbol(Circle())
                        
                    case .bar:
                        BarMark(
                            x: .value("日期", data.date),
                            y: .value("完成", data.value)
                        )
                        .foregroundStyle(Color(hex: habit.colorHex))
                        
                    case .area:
                        AreaMark(
                            x: .value("日期", data.date),
                            y: .value("完成", data.value)
                        )
                        .foregroundStyle(Color(hex: habit.colorHex).opacity(0.3))
                        
                        LineMark(
                            x: .value("日期", data.date),
                            y: .value("完成", data.value)
                        )
                        .foregroundStyle(Color(hex: habit.colorHex))
                    }
                }
                
                // 对比周期数据（如果有）
                if let comparisonData = comparisonPeriodData {
                    ForEach(comparisonData) { data in
                        LineMark(
                            x: .value("日期", data.date),
                            y: .value("完成", data.value)
                        )
                        .foregroundStyle(.orange)
                        .symbol(Circle())
                        .lineStyle(StrokeStyle(lineWidth: 2, dash: [5, 5]))
                    }
                }
            }
            .frame(height: 200)
            .chartYScale(domain: 0...1)
            .chartXAxis {
                AxisMarks(values: .stride(by: .day, count: max(1, dateRange.count / 7))) { value in
                    AxisGridLine()
                    AxisValueLabel(format: .dateTime.month(.abbreviated).day())
                }
            }
            .chartYAxis {
                AxisMarks(values: [0, 1]) { value in
                    AxisGridLine()
                    AxisValueLabel {
                        if let intValue = value.as(Double.self) {
                            Text(intValue == 1 ? "完成" : "未完成")
                        }
                    }
                }
            }
        }
        .padding()
        .background(Color.gray.opacity(0.05))
        .cornerRadius(12)
    }
    
    // MARK: - 统计摘要
    
    private var statisticsSummary: some View {
        VStack(alignment: .leading, spacing: 16) {
            Text("统计摘要")
                .font(.headline)
            
            LazyVGrid(columns: Array(repeating: GridItem(.flexible()), count: 2), spacing: 16) {
                StatCard(
                    title: "完成天数",
                    value: "\(completedDays)",
                    unit: "天",
                    color: .green
                )
                
                StatCard(
                    title: "完成率",
                    value: "\(Int(completionRate * 100))",
                    unit: "%",
                    color: .blue
                )
                
                StatCard(
                    title: "当前连续",
                    value: "\(habit.currentStreak)",
                    unit: "天",
                    color: Color(hex: habit.colorHex)
                )
                
                StatCard(
                    title: "最长连续",
                    value: "\(habit.longestStreak)",
                    unit: "天",
                    color: .orange
                )
            }
        }
    }
    
    // MARK: - 其他图表
    
    private var additionalCharts: some View {
        VStack(alignment: .leading, spacing: 24) {
            // 周完成率图表
            weeklyCompletionChart
            
            // 月度趋势图表
            if dateRange.count > 30 {
                monthlyTrendChart
            }
        }
    }
    
    private var weeklyCompletionChart: some View {
        VStack(alignment: .leading, spacing: 16) {
            Text("周完成率")
                .font(.headline)
            
            Chart(weeklyData) { data in
                BarMark(
                    x: .value("周", data.category),
                    y: .value("完成率", data.value)
                )
                .foregroundStyle(Color(hex: habit.colorHex))
            }
            .frame(height: 150)
            .chartYScale(domain: 0...1)
            .chartYAxis {
                AxisMarks(values: [0, 0.5, 1]) { value in
                    AxisGridLine()
                    AxisValueLabel {
                        if let doubleValue = value.as(Double.self) {
                            Text("\(Int(doubleValue * 100))%")
                        }
                    }
                }
            }
        }
        .padding()
        .background(Color.gray.opacity(0.05))
        .cornerRadius(12)
    }
    
    private var monthlyTrendChart: some View {
        VStack(alignment: .leading, spacing: 16) {
            Text("月度趋势")
                .font(.headline)
            
            Chart(monthlyData) { data in
                LineMark(
                    x: .value("月", data.date),
                    y: .value("完成率", data.value)
                )
                .foregroundStyle(Color(hex: habit.colorHex))
                .symbol(Circle())
                
                AreaMark(
                    x: .value("月", data.date),
                    y: .value("完成率", data.value)
                )
                .foregroundStyle(Color(hex: habit.colorHex).opacity(0.2))
            }
            .frame(height: 150)
            .chartYScale(domain: 0...1)
            .chartXAxis {
                AxisMarks(values: .stride(by: .month)) { value in
                    AxisGridLine()
                    AxisValueLabel(format: .dateTime.month(.abbreviated))
                }
            }
            .chartYAxis {
                AxisMarks(values: [0, 0.5, 1]) { value in
                    AxisGridLine()
                    AxisValueLabel {
                        if let doubleValue = value.as(Double.self) {
                            Text("\(Int(doubleValue * 100))%")
                        }
                    }
                }
            }
        }
        .padding()
        .background(Color.gray.opacity(0.05))
        .cornerRadius(12)
    }
}

// MARK: - 数据计算扩展

extension HabitChartView {

    // 当前周期数据
    private var currentPeriodData: [HabitChartData] {
        let calendar = Calendar.current
        return dateRange.map { date in
            let isCompleted = habit.logs.contains { log in
                calendar.isDate(log.date, inSameDayAs: date) && log.done
            }
            return HabitChartData(
                date: date,
                value: isCompleted ? 1.0 : 0.0,
                category: "当前",
                color: Color(hex: habit.colorHex)
            )
        }
    }

    // 对比周期数据
    private var comparisonPeriodData: [HabitChartData]? {
        guard let comparisonRange = comparisonRange else { return nil }

        let calendar = Calendar.current
        return comparisonRange.enumerated().map { index, date in
            let isCompleted = habit.logs.contains { log in
                calendar.isDate(log.date, inSameDayAs: date) && log.done
            }
            // 使用当前周期的对应日期作为x轴
            let correspondingDate = dateRange[safe: index] ?? date
            return HabitChartData(
                date: correspondingDate,
                value: isCompleted ? 1.0 : 0.0,
                category: "对比",
                color: .orange
            )
        }
    }

    // 周数据
    private var weeklyData: [HabitChartData] {
        let calendar = Calendar.current
        var weeklyStats: [HabitChartData] = []

        // 按周分组
        let weeks = dateRange.chunked(into: 7)
        for (index, week) in weeks.enumerated() {
            let completedDays = week.filter { date in
                habit.logs.contains { log in
                    calendar.isDate(log.date, inSameDayAs: date) && log.done
                }
            }.count

            let completionRate = Double(completedDays) / Double(week.count)
            weeklyStats.append(HabitChartData(
                date: week.first ?? Date(),
                value: completionRate,
                category: "第\(index + 1)周",
                color: Color(hex: habit.colorHex)
            ))
        }

        return weeklyStats
    }

    // 月数据
    private var monthlyData: [HabitChartData] {
        let calendar = Calendar.current
        var monthlyStats: [String: (completed: Int, total: Int, firstDate: Date)] = [:]

        // 按月分组
        for date in dateRange {
            let monthKey = calendar.dateInterval(of: .month, for: date)?.start ?? date
            let monthKeyString = DateFormatter.monthYear.string(from: monthKey)

            if monthlyStats[monthKeyString] == nil {
                monthlyStats[monthKeyString] = (0, 0, monthKey)
            }

            monthlyStats[monthKeyString]?.total += 1

            let isCompleted = habit.logs.contains { log in
                calendar.isDate(log.date, inSameDayAs: date) && log.done
            }

            if isCompleted {
                monthlyStats[monthKeyString]?.completed += 1
            }
        }

        return monthlyStats.map { (key, value) in
            let completionRate = Double(value.completed) / Double(value.total)
            return HabitChartData(
                date: value.firstDate,
                value: completionRate,
                category: key,
                color: Color(hex: habit.colorHex)
            )
        }.sorted { $0.date < $1.date }
    }

    // 完成天数
    private var completedDays: Int {
        let calendar = Calendar.current
        return dateRange.filter { date in
            habit.logs.contains { log in
                calendar.isDate(log.date, inSameDayAs: date) && log.done
            }
        }.count
    }

    // 完成率
    private var completionRate: Double {
        guard !dateRange.isEmpty else { return 0 }
        return Double(completedDays) / Double(dateRange.count)
    }

    // 日期范围描述
    private var dateRangeDescription: String {
        let formatter = DateFormatter()
        formatter.dateFormat = "M月d日"

        guard let startDate = dateRange.first, let endDate = dateRange.last else {
            return ""
        }

        let period = "\(formatter.string(from: startDate)) - \(formatter.string(from: endDate))"

        if let comparisonRange = comparisonRange,
           let compStartDate = comparisonRange.first,
           let compEndDate = comparisonRange.last {
            let compPeriod = "\(formatter.string(from: compStartDate)) - \(formatter.string(from: compEndDate))"
            return "\(period) vs \(compPeriod)"
        }

        return period
    }
}

// MARK: - 图表类型枚举

enum HabitChartType: String, CaseIterable {
    case line = "折线图"
    case bar = "柱状图"
    case area = "面积图"

    var icon: String {
        switch self {
        case .line: return "chart.xyaxis.line"
        case .bar: return "chart.bar.fill"
        case .area: return "chart.line.uptrend.xyaxis"
        }
    }
}

// MARK: - 辅助扩展

extension Array {
    func chunked(into size: Int) -> [[Element]] {
        return stride(from: 0, to: count, by: size).map {
            Array(self[$0..<Swift.min($0 + size, count)])
        }
    }

    subscript(safe index: Index) -> Element? {
        return indices.contains(index) ? self[index] : nil
    }
}

extension DateFormatter {
    static let monthYear: DateFormatter = {
        let formatter = DateFormatter()
        formatter.dateFormat = "yyyy年M月"
        return formatter
    }()
}
